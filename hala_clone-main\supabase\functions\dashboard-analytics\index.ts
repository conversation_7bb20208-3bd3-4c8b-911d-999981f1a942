import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    console.log('Dashboard analytics function called')
    console.log('Request URL:', req.url)
    console.log('Request method:', req.method)

    // Get authenticated user with detailed error handling
    console.log('Getting user from request...')
    let user, profile

    try {
      const result = await getUserFromRequest(req)
      user = result.user
      profile = result.profile
      console.log('User authenticated:', user.id)
      console.log('User profile role:', profile.role)
    } catch (profileError) {
      console.log('Profile error caught:', profileError.message)

      // Try to get just the user without profile
      const supabase = createSupabaseClient()
      const authHeader = req.headers.get('authorization')

      if (!authHeader) {
        return errorResponse('No authorization header', 401)
      }

      const token = authHeader.replace('Bearer ', '')
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(token)

      if (authError || !authUser) {
        return errorResponse('Invalid token', 401)
      }

      console.log('User authenticated but no profile found. User ID:', authUser.id)
      console.log('User email:', authUser.email)

      // Check if profile exists with different query
      const { data: allProfiles, error: allProfilesError } = await supabase
        .from('profiles')
        .select('*')

      console.log('All profiles count:', allProfiles?.length || 0)
      console.log('All profiles error:', allProfilesError?.message || 'none')

      if (allProfiles && allProfiles.length > 0) {
        console.log('Sample profile:', JSON.stringify(allProfiles[0]))

        // Check if there's a profile with this user ID
        const userProfile = allProfiles.find(p => p.id === authUser.id)
        if (userProfile) {
          console.log('Found profile for user:', JSON.stringify(userProfile))
          user = authUser
          profile = userProfile
        } else {
          console.log('No profile found for user ID:', authUser.id)
          return errorResponse('Profile not found for authenticated user', 404)
        }
      } else {
        return errorResponse('No profiles exist in database', 404)
      }
    }

    if (profile.role !== 'business') {
      console.log('Access denied - user role is not business:', profile.role)
      return errorResponse('Unauthorized. Business role required', 403)
    }

    const url = new URL(req.url)
    const timeRange = url.searchParams.get('timeRange') || '30d'
    console.log('Time range requested:', timeRange)

    console.log('Creating Supabase client...')
    const supabase = createSupabaseClient()

    // Calculate start date based on time range
    const startDate = new Date()
    switch (timeRange) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(startDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(startDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      default:
        startDate.setFullYear(2000)
    }

    // Get business items
    console.log('Fetching business items for user:', user.id)
    const { data: businessItems, error: itemsError } = await supabase
      .from('items')
      .select('*')
      .eq('creator_id', user.id)
      .eq('is_active', true)

    if (itemsError) {
      console.error('Error fetching items:', itemsError)
      return errorResponse('Failed to fetch analytics', 500)
    }

    console.log('Business items found:', businessItems?.length || 0)

    const itemIds = businessItems.map(item => item.id)

    // Get transfers for these items within the time range
    const { data: transfers, error: transfersError } = await supabase
      .from('transfers')
      .select('*')
      .in('item_id', itemIds)
      .gte('transferred_at', startDate.toISOString())

    if (transfersError) {
      console.error('Error fetching transfers:', transfersError)
      return errorResponse('Failed to fetch analytics', 500)
    }

    const itemsSold = transfers.length

    // Calculate unique customers
    const newCustomers = new Set<string>()
    transfers.forEach((transfer) => {
      if (transfer.recipient_id && transfer.recipient_id !== user.id) {
        newCustomers.add(transfer.recipient_id)
      }
    })

    const totalRevenue = transfers.length // Simplified - in real app would calculate actual revenue

    // Calculate average daily trades
    const daysDiff = Math.max(1, Math.ceil((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)))
    const avgDailyTrades = transfers.length / daysDiff

    // Sales by day
    const salesByDay: Record<string, number> = {}
    const currentDate = new Date()

    for (let i = 0; i < daysDiff; i++) {
      const date = new Date()
      date.setDate(currentDate.getDate() - i)
      const dateString = date.toISOString().split('T')[0]
      salesByDay[dateString] = 0
    }

    transfers.forEach((transfer) => {
      const date = new Date(transfer.transferred_at).toISOString().split('T')[0]
      if (salesByDay[date] !== undefined) {
        salesByDay[date]++
      }
    })

    const salesOverTime = Object.entries(salesByDay)
      .map(([date, count]) => ({ date, count }))
      .reverse()

    // Category distribution (by brand)
    const categoryDistribution: Record<string, number> = {}
    transfers.forEach((transfer) => {
      const item = businessItems.find(i => i.id === transfer.item_id)
      if (item) {
        const category = item.brand || 'Unknown'
        categoryDistribution[category] = (categoryDistribution[category] || 0) + 1
      }
    })

    const distributionByCategory = Object.entries(categoryDistribution)
      .map(([category, count]) => ({ category, count }))

    // Top selling items
    const itemSales: Record<string, number> = {}
    transfers.forEach((transfer) => {
      itemSales[transfer.item_id] = (itemSales[transfer.item_id] || 0) + 1
    })

    const topSellingItems = Object.entries(itemSales)
      .map(([itemId, sales]) => {
        const item = businessItems.find(i => i.id === itemId)
        return {
          id: itemId,
          name: item ? item.name : 'Unknown Item',
          sales,
          revenue: sales, // Simplified
        }
      })
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5)

    return successResponse({
      analytics: {
        itemsSold,
        newCustomers: newCustomers.size,
        totalRevenue,
        avgDailyTrades,
        salesOverTime,
        distributionByCategory,
        topSellingItems,
        topCustomers: [], // Simplified for now
      },
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    console.error('Error stack:', error.stack)
    console.error('Error message:', error.message)
    console.error('Error name:', error.name)

    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse(`Internal server error: ${error.message}`, 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/dashboard-analytics' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
