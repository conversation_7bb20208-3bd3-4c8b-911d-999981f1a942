#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create a profile for the test user
 * Run with: node create-profile.js
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://dcdslxzhypxpledhkvtw.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8'

const supabase = createClient(supabaseUrl, supabaseKey)

async function createProfile() {
  console.log('🔍 Checking for existing users...')
  
  // First, let's see what users exist in auth
  try {
    // Login to get the user ID
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: '#rafaEl21'
    })

    if (authError) {
      console.error('❌ Login failed:', authError.message)
      return
    }

    console.log('✅ Login successful')
    console.log('👤 User ID:', authData.user.id)
    console.log('📧 User email:', authData.user.email)

    // Check if profile already exists
    const { data: existingProfiles, error: profileCheckError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)

    if (profileCheckError) {
      console.error('❌ Error checking profiles:', profileCheckError.message)
      return
    }

    if (existingProfiles && existingProfiles.length > 0) {
      console.log('✅ Profile already exists:', existingProfiles[0])
      return
    }

    console.log('📝 Creating profile...')

    // Create the profile
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email: authData.user.email,
        full_name: 'Test Business User',
        role: 'business',
        business_name: 'Test Business',
        business_vat_number: '123456789',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createError) {
      console.error('❌ Error creating profile:', createError.message)
      console.error('Error details:', createError)
      return
    }

    console.log('✅ Profile created successfully!')
    console.log('📋 Profile data:', newProfile)

    // Verify the profile was created
    const { data: verifyProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (verifyError) {
      console.error('❌ Error verifying profile:', verifyError.message)
      return
    }

    console.log('✅ Profile verification successful!')
    console.log('📋 Verified profile:', verifyProfile)

  } catch (error) {
    console.error('💥 Unexpected error:', error.message)
  }
}

createProfile()
