"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function TestAnalyticsDebug() {
  const [authState, setAuthState] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)
  const [analyticsResult, setAnalyticsResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const supabase = createBrowserClient()
      const { data: { session } } = await supabase.auth.getSession()
      setAuthState(session)

      if (session) {
        // Get user profile
        const { data: profileData, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single()

        console.log('Profile query result:', { profileData, error })

        if (error) {
          console.error('Profile error:', error)
          // If profile doesn't exist, show the error
          setProfile({ error: error.message })
        } else {
          setProfile(profileData)
        }
      }
    } catch (error) {
      console.error('Auth check error:', error)
    }
  }

  const testAnalytics = async () => {
    setLoading(true)
    try {
      console.log('Testing analytics function...')

      // Get current session
      const supabase = createBrowserClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setAnalyticsResult({ error: 'No session found' })
        return
      }

      console.log('Session found, testing direct fetch...')

      // Test direct fetch to Edge Function
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/dashboard-analytics?timeRange=30d`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          }
        }
      )

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      const result = await response.json()
      console.log('Response data:', result)

      setAnalyticsResult({
        status: response.status,
        ok: response.ok,
        data: result
      })
    } catch (error) {
      console.error('Analytics test error:', error)
      setAnalyticsResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const login = async () => {
    try {
      const supabase = createBrowserClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: '#rafaEl21'
      })

      if (error) {
        console.error('Login error:', error)
      } else {
        console.log('Login successful:', data)
        checkAuth()
      }
    } catch (error) {
      console.error('Login error:', error)
    }
  }

  const createProfile = async () => {
    try {
      if (!authState?.user) {
        alert('Please login first')
        return
      }

      const supabase = createBrowserClient()
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: authState.user.id,
          email: authState.user.email || '<EMAIL>',
          full_name: 'Test Business User',
          role: 'business',
          business_name: 'Test Business',
          business_vat_number: '123456789'
        })
        .select()
        .single()

      if (error) {
        console.error('Profile creation error:', error)
        alert(`Error creating profile: ${error.message}`)
      } else {
        console.log('Profile created:', data)
        alert('Profile created successfully!')
        checkAuth()
      }
    } catch (error) {
      console.error('Profile creation error:', error)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Analytics Debug Page</h1>
      
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Authentication Status</h2>
        <div className="space-y-2">
          <p><strong>Authenticated:</strong> {authState ? 'Yes' : 'No'}</p>
          {authState && (
            <>
              <p><strong>User ID:</strong> {authState.user?.id}</p>
              <p><strong>Email:</strong> {authState.user?.email}</p>
              <p><strong>Access Token:</strong> {authState.access_token ? 'Present' : 'Missing'}</p>
            </>
          )}
        </div>
        
        {!authState && (
          <Button onClick={login} className="mt-4">
            Login with Test Credentials
          </Button>
        )}
      </Card>

      {profile && !profile.error && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">User Profile</h2>
          <div className="space-y-2">
            <p><strong>Role:</strong> {profile.role}</p>
            <p><strong>Full Name:</strong> {profile.full_name}</p>
            <p><strong>Business Name:</strong> {profile.business_name || 'N/A'}</p>
          </div>
        </Card>
      )}

      {profile?.error && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Profile Issue</h2>
          <div className="space-y-4">
            <p className="text-red-600"><strong>Error:</strong> {profile.error}</p>
            <p className="text-sm text-gray-600">
              It looks like your user account doesn't have a profile record. This is needed for the analytics function.
            </p>
            <Button onClick={createProfile} className="bg-blue-600 hover:bg-blue-700">
              Create Business Profile
            </Button>
          </div>
        </Card>
      )}

      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Analytics Test</h2>
        <Button 
          onClick={testAnalytics} 
          disabled={loading || !authState}
          className="mb-4"
        >
          {loading ? 'Testing...' : 'Test Analytics Function'}
        </Button>
        
        {analyticsResult && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(analyticsResult, null, 2)}
            </pre>
          </div>
        )}
      </Card>
    </div>
  )
}
