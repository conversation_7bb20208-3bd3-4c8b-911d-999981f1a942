"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function TestAnalyticsDebug() {
  const [authState, setAuthState] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)
  const [analyticsResult, setAnalyticsResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [allProfiles, setAllProfiles] = useState<any>(null)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const supabase = createBrowserClient()
      const { data: { session } } = await supabase.auth.getSession()
      setAuthState(session)

      if (session) {
        // Get user profile - try without .single() first to see all results
        const { data: profileData, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)

        console.log('Profile query result:', { profileData, error })
        console.log('User ID:', session.user.id)

        if (error) {
          console.error('Profile error:', error)
          setProfile({ error: error.message })
        } else if (!profileData || profileData.length === 0) {
          console.log('No profile found for user')
          setProfile({ error: 'No profile found' })
        } else {
          console.log('Profile found:', profileData[0])
          setProfile(profileData[0])
        }
      }
    } catch (error) {
      console.error('Auth check error:', error)
    }
  }

  const testAnalytics = async () => {
    setLoading(true)
    try {
      console.log('Testing analytics function...')

      // Get current session
      const supabase = createBrowserClient()
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        setAnalyticsResult({ error: 'No session found' })
        return
      }

      console.log('Session found, testing direct fetch...')

      // Test direct fetch to Edge Function
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/dashboard-analytics?timeRange=30d`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          }
        }
      )

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      const result = await response.json()
      console.log('Response data:', result)

      setAnalyticsResult({
        status: response.status,
        ok: response.ok,
        data: result
      })
    } catch (error) {
      console.error('Analytics test error:', error)
      setAnalyticsResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const login = async () => {
    try {
      const supabase = createBrowserClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: '#rafaEl21'
      })

      if (error) {
        console.error('Login error:', error)
      } else {
        console.log('Login successful:', data)
        checkAuth()
      }
    } catch (error) {
      console.error('Login error:', error)
    }
  }

  const createProfile = async () => {
    try {
      if (!authState?.user) {
        alert('Please login first')
        return
      }

      const supabase = createBrowserClient()

      // First try to update existing profile
      const { data: updateData, error: updateError } = await supabase
        .from('profiles')
        .update({
          email: authState.user.email || '<EMAIL>',
          full_name: 'Test Business User',
          role: 'business',
          business_name: 'Test Business',
          business_vat_number: '123456789',
          updated_at: new Date().toISOString()
        })
        .eq('id', authState.user.id)
        .select()

      if (updateError) {
        console.log('Update failed, trying insert:', updateError)

        // If update fails, try insert
        const { data: insertData, error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: authState.user.id,
            email: authState.user.email || '<EMAIL>',
            full_name: 'Test Business User',
            role: 'business',
            business_name: 'Test Business',
            business_vat_number: '123456789'
          })
          .select()

        if (insertError) {
          console.error('Profile insert error:', insertError)
          alert(`Error creating profile: ${insertError.message}`)
        } else {
          console.log('Profile created:', insertData)
          alert('Profile created successfully!')
          checkAuth()
        }
      } else {
        console.log('Profile updated:', updateData)
        alert('Profile updated successfully!')
        checkAuth()
      }
    } catch (error) {
      console.error('Profile creation error:', error)
    }
  }

  const checkAllProfiles = async () => {
    try {
      const supabase = createBrowserClient()
      const { data, error } = await supabase
        .from('profiles')
        .select('*')

      if (error) {
        console.error('Error fetching all profiles:', error)
        setAllProfiles({ error: error.message })
      } else {
        console.log('All profiles:', data)
        setAllProfiles(data)
      }
    } catch (error) {
      console.error('Error:', error)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Analytics Debug Page</h1>
      
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Authentication Status</h2>
        <div className="space-y-2">
          <p><strong>Authenticated:</strong> {authState ? 'Yes' : 'No'}</p>
          {authState && (
            <>
              <p><strong>User ID:</strong> {authState.user?.id}</p>
              <p><strong>Email:</strong> {authState.user?.email}</p>
              <p><strong>Access Token:</strong> {authState.access_token ? 'Present' : 'Missing'}</p>
            </>
          )}
        </div>
        
        {!authState && (
          <Button onClick={login} className="mt-4">
            Login with Test Credentials
          </Button>
        )}
      </Card>

      {profile && !profile.error && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">User Profile</h2>
          <div className="space-y-2">
            <p><strong>Role:</strong> {profile.role}</p>
            <p><strong>Full Name:</strong> {profile.full_name}</p>
            <p><strong>Business Name:</strong> {profile.business_name || 'N/A'}</p>
          </div>
        </Card>
      )}

      {profile?.error && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Profile Issue</h2>
          <div className="space-y-4">
            <p className="text-red-600"><strong>Error:</strong> {profile.error}</p>
            <p className="text-sm text-gray-600">
              It looks like your user account doesn't have a profile record. This is needed for the analytics function.
            </p>
            <div className="space-x-2">
              <Button onClick={createProfile} className="bg-blue-600 hover:bg-blue-700">
                Update/Create Business Profile
              </Button>
              <Button onClick={checkAllProfiles} variant="outline">
                Check All Profiles
              </Button>
            </div>
          </div>
        </Card>
      )}

      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Analytics Test</h2>
        <Button 
          onClick={testAnalytics} 
          disabled={loading || !authState}
          className="mb-4"
        >
          {loading ? 'Testing...' : 'Test Analytics Function'}
        </Button>
        
        {analyticsResult && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(analyticsResult, null, 2)}
            </pre>
          </div>
        )}
      </Card>

      {allProfiles && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">All Profiles Debug</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-64">
            {JSON.stringify(allProfiles, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  )
}
