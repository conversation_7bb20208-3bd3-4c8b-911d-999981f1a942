#!/usr/bin/env node

/**
 * Test script to check profile access from different contexts
 * Run with: node test-profile-access.js
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://dcdslxzhypxpledhkvtw.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgwNjgzOSwiZXhwIjoyMDY1MzgyODM5fQ.UEGh9i1u6X_FiW-LxjuM7YcTnL9wQtt5NMTqfwBDIa0'

async function testProfileAccess() {
  console.log('🧪 Testing profile access...')

  // Test 1: With anon key (like Edge Functions use)
  console.log('\n1️⃣ Testing with anon key (Edge Function context)...')
  const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey)
  
  try {
    const { data: profiles1, error: error1 } = await supabaseAnon
      .from('profiles')
      .select('*')
    
    console.log('Anon key - Profiles found:', profiles1?.length || 0)
    if (error1) {
      console.log('Anon key - Error:', error1.message)
    }
  } catch (err) {
    console.log('Anon key - Exception:', err.message)
  }

  // Test 2: With service role key
  console.log('\n2️⃣ Testing with service role key...')
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
  
  try {
    const { data: profiles2, error: error2 } = await supabaseService
      .from('profiles')
      .select('*')
    
    console.log('Service key - Profiles found:', profiles2?.length || 0)
    if (error2) {
      console.log('Service key - Error:', error2.message)
    } else if (profiles2 && profiles2.length > 0) {
      console.log('Service key - Sample profile:', profiles2[0])
    }
  } catch (err) {
    console.log('Service key - Exception:', err.message)
  }

  // Test 3: With authenticated user context
  console.log('\n3️⃣ Testing with authenticated user...')
  const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey)
  
  try {
    // Login first
    const { data: authData, error: authError } = await supabaseAuth.auth.signInWithPassword({
      email: '<EMAIL>',
      password: '#rafaEl21'
    })

    if (authError) {
      console.log('Auth error:', authError.message)
      return
    }

    console.log('User authenticated:', authData.user.id)

    // Now try to get profiles
    const { data: profiles3, error: error3 } = await supabaseAuth
      .from('profiles')
      .select('*')
    
    console.log('Authenticated - Profiles found:', profiles3?.length || 0)
    if (error3) {
      console.log('Authenticated - Error:', error3.message)
    } else if (profiles3 && profiles3.length > 0) {
      console.log('Authenticated - Sample profile:', profiles3[0])
    }

    // Test specific user profile
    const { data: userProfile, error: userError } = await supabaseAuth
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (userError) {
      console.log('User profile error:', userError.message)
    } else {
      console.log('User profile found:', userProfile)
    }

  } catch (err) {
    console.log('Authenticated - Exception:', err.message)
  }
}

testProfileAccess()
