#!/usr/bin/env node

/**
 * Test script for analytics function
 * Run with: node test-analytics.js
 */

const BASE_URL = 'https://dcdslxzhypxpledhkvtw.supabase.co/functions/v1'

async function testAnalytics() {
  console.log('🧪 Testing analytics function...')
  
  // First, let's try to login and get a token
  console.log('📝 Attempting login...')
  
  try {
    const loginResponse = await fetch(`${BASE_URL}/auth-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRjZHNseHpoeXB4cGxlZGhrdnR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MDY4MzksImV4cCI6MjA2NTM4MjgzOX0.Qzn5ytootgoWLpsU6Jz5a5RgzAIiZiEWMW2nQLhKzq8'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '#rafaEl21'
      })
    })

    console.log('Login response status:', loginResponse.status)
    console.log('Login response headers:', Object.fromEntries(loginResponse.headers.entries()))

    const loginData = await loginResponse.json()

    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginData)
      return
    }

    console.log('✅ Login successful')
    
    if (!loginData.session?.access_token) {
      console.log('❌ No access token in response')
      return
    }

    const token = loginData.session.access_token
    console.log('🔑 Got access token')

    // Now test the analytics function
    console.log('📊 Testing analytics function...')
    
    const analyticsResponse = await fetch(`${BASE_URL}/dashboard-analytics?timeRange=30d`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    })

    const analyticsData = await analyticsResponse.json()
    
    if (analyticsResponse.ok) {
      console.log('✅ Analytics function - Success:', analyticsData)
    } else {
      console.log('❌ Analytics function - Error:', analyticsData)
      console.log('   Status:', analyticsResponse.status)
      console.log('   Status Text:', analyticsResponse.statusText)
    }

  } catch (error) {
    console.log('💥 Network Error:', error.message)
  }
}

testAnalytics()
